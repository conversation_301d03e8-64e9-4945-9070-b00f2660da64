import React, {useEffect, useRef, useState} from 'react';
import {
    Keyboard,
    KeyboardAvoidingView,
    LayoutAnimation,
    Platform,
    ScrollView,
    TouchableOpacity,
    TouchableWithoutFeedback,
    View,
} from 'react-native';
import {Avatar, Button, H3, Input, Sheet, Text, XStack, YStack} from 'tamagui';
import {AntDesign, Feather} from '@expo/vector-icons';
import {useMutation, useQueryClient} from '@tanstack/react-query';
import {commentOnPost, reactToComment} from '@/api/postsAPI';

type NewComment = {
    postID: string;
    comment: string;
    replyToCommentID?: string | null;
};

type Comment = {
    id: string;
    username: string;
    text: string;
    commenterProfilePicture?: string;
    createdAt: string;
    likes: number;
    likedByUser: boolean;
    replyToCommentID?: string | null;
};

type CommentSectionProps = {
    postID: string;
    comments: Comment[];
    hideActionButtons?: boolean;
    showComments: boolean;
    setShowComments: React.Dispatch<React.SetStateAction<boolean>>;
    userProfilePicture: string;
};

const SingleComment: React.FC<{
    comment: Comment;
    isReply?: boolean;
    onReply: (comment: Comment) => void;
    onLike: (commentId: string, reactionType: 'Like' | 'None') => void;
    expandedComments: { [key: string]: boolean };
    toggleExpandComment: (id: string) => void;
    level?: number;
}> = ({comment, isReply = false, onReply, onLike, expandedComments, toggleExpandComment, level = 0}) => {
    return (
        <YStack
            marginBottom="$3"
            marginLeft={isReply ? 28 : 0}
            position="relative"
            opacity={isReply ? 0.9 : 1}
        >
            {isReply && (
                <>
                    <View
                        style={{
                            position: 'absolute',
                            left: -24,
                            top: 16,
                            width: 24,
                            height: 1.5,
                            backgroundColor: '#333',
                            opacity: 0.5,
                        }}
                    />
                    <View
                        style={{
                            position: 'absolute',
                            left: -24,
                            top: 0,
                            bottom: -12,
                            width: 1.5,
                            backgroundColor: '#333',
                            opacity: 0.5,
                        }}
                    />
                </>
            )}
            <XStack
                backgroundColor={isReply ? '#111' : 'transparent'}
                borderRadius="$4"
                paddingHorizontal={isReply ? '$3' : 0}
                paddingVertical={isReply ? '$2' : 0}
                space="$3"
                alignItems="center"
            >
                <Avatar
                    size={isReply ? '$2' : '$3'}
                    circular
                    borderWidth={1}
                    borderColor="$gray8"
                >
                    <Avatar.Image src={comment.commenterProfilePicture}/>
                    <Avatar.Fallback backgroundColor="#fed900"/>
                </Avatar>

                <YStack flex={1} space="$1">
                    <XStack alignItems="center" space="$2">
                        <Text
                            color="white"
                            fontWeight="bold"
                            fontSize={isReply ? '$1' : '$2'}
                        >
                            {comment.username}
                        </Text>
                        <Text color="$gray11" fontSize="$1">·</Text>
                        <Text color="$gray11" fontSize="$1">
                            {new Date(comment.createdAt).toLocaleDateString()}
                        </Text>
                    </XStack>

                    <Text
                        color="$gray11"
                        fontSize={isReply ? '$1' : '$2'}
                        numberOfLines={expandedComments[comment.id] ? undefined : 2}
                        ellipsizeMode="tail"
                    >
                        {comment.text}
                    </Text>

                    <XStack space="$4" alignItems="center" justifyContent="space-between">
                        {/* Reply and Read More on the left */}
                        <XStack space="$2" alignItems="center">
                            {comment.text.length > 100 && (
                                <TouchableOpacity onPress={() => toggleExpandComment(comment.id)}>
                                    <Text color="#fed900" fontSize="$1">
                                        {expandedComments[comment.id] ? 'Show less' : 'Read more'}
                                    </Text>
                                </TouchableOpacity>
                            )}

                            <TouchableOpacity onPress={() => onReply(comment)}>
                                <Text color="#fed900" fontSize="$1">
                                    Reply
                                </Text>
                            </TouchableOpacity>
                        </XStack>

                        {/* Like button and count on the far right */}
                        <XStack space="$2" alignItems="center">
                            <TouchableOpacity
                                onPress={() => onLike(comment.id, comment.likedByUser ? 'None' : 'Like')}
                            >
                                {comment.likedByUser ? (
                                    <AntDesign name="heart" color="#ff3b30" size={isReply ? 14 : 16}/>
                                ) : (
                                    <AntDesign name="hearto" color="gray" size={isReply ? 14 : 16}/>
                                )}
                            </TouchableOpacity>
                            {comment.likes > 0 && (
                                <Text color="$gray11" fontSize={isReply ? '$1' : '$2'}>
                                    {comment.likes}
                                </Text>
                            )}
                        </XStack>
                    </XStack>
                </YStack>
            </XStack>
        </YStack>
    );
};

const CommentSection: React.FC<CommentSectionProps> = ({
                                                           postID,
                                                           comments,
                                                           hideActionButtons = false,
                                                           showComments,
                                                           setShowComments,
                                                           userProfilePicture,
                                                       }) => {
    const [newComment, setNewComment] = useState('');
    const [replyToComment, setReplyToComment] = useState<Comment | null>(null);
    const [expandedComments, setExpandedComments] = useState<{ [key: string]: boolean }>({});
    const queryClient = useQueryClient();
    const scrollViewRef = useRef<ScrollView>(null);
    const inputRef = useRef<any>(null);
    const [keyboardHeight, setKeyboardHeight] = useState(0);
    const [contentHeight, setContentHeight] = useState(0);

    const {mutateAsync: mutateAsyncComment} = useMutation({
        mutationFn: (params: NewComment) =>
            commentOnPost(params.postID, params.comment, params.replyToCommentID),
        onSuccess: () => {
            queryClient.invalidateQueries({queryKey: ['userFeed']});
        },
    });

    const {mutateAsync: mutateAsyncReact} = useMutation({
        mutationFn: ({postId, commentId, reactionType}: {
            postId: string;
            commentId: string;
            reactionType: 'Like' | 'None'
        }) =>
            reactToComment(postId, commentId, reactionType),
        onSuccess: () => {
            queryClient.invalidateQueries({queryKey: ['userFeed']});
        },
    });

    useEffect(() => {
        const keyboardDidShowListener = Keyboard.addListener(
            'keyboardDidShow',
            (e) => {
                LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
                setKeyboardHeight(e.endCoordinates.height);
            }
        );
        const keyboardDidHideListener = Keyboard.addListener(
            'keyboardDidHide',
            () => {
                LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
                setKeyboardHeight(0);
            }
        );

        return () => {
            keyboardDidShowListener.remove();
            keyboardDidHideListener.remove();
        };
    }, []);

    const handleCommentSubmit = async (postID: string, comment: string) => {
        if (!comment.trim()) {
            return;
        }
        const replyToCommentID = replyToComment ? replyToComment.id : null;
        await mutateAsyncComment({postID, comment, replyToCommentID});
        setNewComment('');
        setReplyToComment(null);
    };

    const handleLike = async (commentId: string, reactionType: 'Like' | 'None') => {
        await mutateAsyncReact({postId: postID, commentId, reactionType});
    };

    const toggleExpandComment = (id: string) => {
        setExpandedComments((prev) => ({
            ...prev,
            [id]: !prev[id],
        }));
    };

    const handleReply = (comment: Comment) => {
        setReplyToComment(comment);
        inputRef.current?.focus();
    };

    const organizeComments = (commentsArray: Comment[]) => {
        const commentMap = new Map<string, Comment[]>();
        const rootComments: Comment[] = [];

        const sortedComments = [...commentsArray].sort(
            (a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
        );

        sortedComments.forEach(comment => {
            if (!comment.replyToCommentID) {
                rootComments.push(comment);
            } else {
                if (!commentMap.has(comment.replyToCommentID)) {
                    commentMap.set(comment.replyToCommentID, []);
                }
                commentMap.get(comment.replyToCommentID)?.push(comment);
            }
        });

        return {rootComments, commentMap};
    };

    const {rootComments, commentMap} = organizeComments(comments);

    const handleBackgroundPress = () => {
        if (replyToComment) {
            setReplyToComment(null);
        }
        Keyboard.dismiss();
    };

    return (
        <Sheet
            modal
            open={showComments}
            onOpenChange={setShowComments}
            snapPoints={[60]}
            dismissOnSnapToBottom
            position={0}
            zIndex={100000}
        >
            <Sheet.Overlay/>
            <Sheet.Frame
                backgroundColor="black"
                height="100%"
                // Add paddingBottom for iOS keyboard + extra spacing, and base spacing when keyboard is closed
                paddingBottom={Platform.OS === 'ios' ? (keyboardHeight > 0 ? keyboardHeight + 20 : 30) : 0}
            >
                <Sheet.Handle backgroundColor="$gray8"/>
                <TouchableWithoutFeedback onPress={handleBackgroundPress}>
                    <YStack
                        padding="$4"
                        flex={1}
                        // Remove bottom padding on iOS since we handle it at Sheet.Frame level
                        paddingBottom={Platform.OS === 'ios' ? 0 : '$4'}
                    >
                        <H3 color="white" marginBottom="$4">Comments</H3>
                        <ScrollView
                            style={{flex: 1}}
                            ref={scrollViewRef}
                            keyboardShouldPersistTaps="always"
                            onContentSizeChange={(_, height) => setContentHeight(height)}
                            contentContainerStyle={{
                                paddingBottom: Platform.OS === 'android' ? (keyboardHeight > 0 ? keyboardHeight + 20 : 30) : 20
                            }}
                        >
                            {rootComments.map((comment) => (
                                <YStack key={comment.id}>
                                    <SingleComment
                                        comment={comment}
                                        onReply={handleReply}
                                        onLike={handleLike}
                                        expandedComments={expandedComments}
                                        toggleExpandComment={toggleExpandComment}
                                        level={0}
                                    />
                                    {commentMap.get(comment.id)?.map((reply) => (
                                        <SingleComment
                                            key={reply.id}
                                            comment={reply}
                                            isReply={true}
                                            onReply={handleReply}
                                            onLike={handleLike}
                                            expandedComments={expandedComments}
                                            toggleExpandComment={toggleExpandComment}
                                            level={1}
                                        />
                                    ))}
                                </YStack>
                            ))}
                        </ScrollView>

                        {!hideActionButtons && (
                            <YStack
                                paddingTop="$4"
                                borderTopWidth={1}
                                borderTopColor="$gray8"
                                marginTop="auto"
                            >
                                {replyToComment && (
                                    <XStack
                                        backgroundColor="$gray8"
                                        padding="$2"
                                        marginBottom="$2"
                                        borderRadius="$2"
                                    >
                                        <YStack flex={1}>
                                            <Text color="$gray11" fontSize="$1">
                                                Replying to:
                                            </Text>
                                            <Text color="white" numberOfLines={2} ellipsizeMode="tail">
                                                {replyToComment.text}
                                            </Text>
                                        </YStack>
                                        <TouchableOpacity onPress={() => setReplyToComment(null)}>
                                            <Feather name="x" color="white" size={20}/>
                                        </TouchableOpacity>
                                    </XStack>
                                )}
                                <XStack alignItems="center">
                                    <Input
                                        ref={inputRef}
                                        flex={1}
                                        placeholder={
                                            replyToComment
                                                ? 'Write a reply...'
                                                : 'Add a comment...'
                                        }
                                        value={newComment}
                                        onChangeText={setNewComment}
                                        color="white"
                                        placeholderTextColor="$gray11"
                                    />
                                    <Button
                                        marginLeft="$2"
                                        onPress={() => handleCommentSubmit(postID, newComment)}
                                        backgroundColor="#fed900"
                                        color="black"
                                    >
                                        {replyToComment ? 'Reply' : 'Post'}
                                    </Button>
                                </XStack>
                            </YStack>
                        )}
                    </YStack>
                </TouchableWithoutFeedback>
            </Sheet.Frame>
        </Sheet>
    );
};

export default CommentSection;